# Wordlist Sources Configuration for ipcrawler
# Defines all wordlist sources and their integration parameters

sources:
  seclists:
    name: "<PERSON><PERSON><PERSON><PERSON>"
    description: "The security tester's companion - comprehensive wordlists"
    repository: "https://github.com/danielmiessler/SecLists"
    install_methods:
      debian: "sudo apt install seclists"
      macos: "brew install seclists"
      manual: "git clone https://github.com/danielmiessler/SecLists.git"
    default_paths:
      - "/usr/share/seclists"
      - "/usr/local/share/seclists" 
      - "/opt/seclists"
    priority: 3
    enabled: true

  jhaddix_all:
    name: "Jhaddix All.txt"
    description: "The king of web content discovery - massive comprehensive wordlist"
    repository: "https://gist.github.com/jhaddix/b80ea67d85c13206125806f0828f4d10"
    url: "https://gist.githubusercontent.com/jhaddix/b80ea67d85c13206125806f0828f4d10/raw/c81a34fe84731430741e74c7ca0ee9b77c63e523/all.txt"
    install_methods:
      curl: "curl -s {url} -o {path}/jhaddix-all.txt"
    default_paths:
      - "/usr/share/wordlists/jhaddix"
      - "/usr/local/share/wordlists/jhaddix"
      - "/opt/wordlists/jhaddix"
    priority: 5  # Highest priority - king of discovery
    enabled: true
    categories:
      - "web_directories"
      - "web_files"
      - "web_comprehensive"
    tags: ["comprehensive", "discovery", "jhaddix", "massive", "premium"]

  onelistforall:
    name: "OneListForAll"
    description: "Comprehensive web fuzzing wordlists collection"
    repository: "https://github.com/six2dez/OneListForAll"
    install_methods:
      git: "git clone https://github.com/six2dez/OneListForAll.git"
    default_paths:
      - "/usr/share/wordlists/onelistforall"
      - "/usr/local/share/wordlists/onelistforall"
      - "/opt/wordlists/onelistforall"
    priority: 4  # High priority - comprehensive collection
    enabled: true
    categories:
      - "web_directories"
      - "web_files"
      - "web_parameters"
      - "web_technologies"
    tags: ["comprehensive", "fuzzing", "collection", "onelistforall"]

# Technology-specific wordlist mappings for enhanced selection
technology_wordlist_mapping:
  # High-priority mappings for premium wordlists
  wordpress:
    preferred_sources: ["jhaddix_all", "onelistforall", "seclists"]
    fallback_patterns: ["wp-", "wordpress", "cms"]
  
  drupal:
    preferred_sources: ["jhaddix_all", "onelistforall", "seclists"]
    fallback_patterns: ["drupal", "cms"]
    
  joomla:
    preferred_sources: ["jhaddix_all", "onelistforall", "seclists"] 
    fallback_patterns: ["joomla", "cms"]

  # Web servers and frameworks
  apache:
    preferred_sources: ["jhaddix_all", "seclists", "onelistforall"]
    fallback_patterns: ["apache", "web", "common"]
    
  nginx:
    preferred_sources: ["jhaddix_all", "seclists", "onelistforall"]
    fallback_patterns: ["nginx", "web", "common"]

  tomcat:
    preferred_sources: ["jhaddix_all", "onelistforall", "seclists"]
    fallback_patterns: ["tomcat", "java", "jsp"]

  # Default comprehensive discovery
  default:
    preferred_sources: ["jhaddix_all", "onelistforall", "seclists"]
    fallback_patterns: ["common", "directory", "web"]

# Installation configuration
installation:
  base_directory: "/usr/share/wordlists"
  backup_directory: "/usr/local/share/wordlists"
  create_symlinks: true
  verify_checksums: true
  update_frequency: "monthly"
  
# Catalog configuration
catalog:
  filename: "unified_catalog.yaml"
  compression: true
  include_metadata: true
  rebuild_threshold: "7 days"
  cache_size_limit: "50MB"