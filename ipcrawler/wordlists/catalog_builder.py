"""
Unified Catalog Builder for ipcrawler
Builds consolidated wordlist catalogs from multiple sources
"""

import os
import yaml
from pathlib import Path
from typing import Dict, List, Optional, Set
from datetime import datetime
import hashlib


class UnifiedCatalogBuilder:
    """Builds unified wordlist catalogs from multiple sources"""
    
    def __init__(self, config: Dict):
        """Initialize catalog builder with configuration"""
        self.config = config
        self.catalog = {
            'metadata': {
                'generator_version': '2.0',
                'generated_time': datetime.now().isoformat(),
                'sources': [],
                'total_wordlists': 0
            },
            'wordlists': {}
        }
    
    def build_catalog(self) -> bool:
        """Build unified catalog from all configured sources"""
        print("🔄 Building unified wordlist catalog...")
        
        # First, check for and prioritize the consolidated mega-wordlist
        self._check_for_mega_wordlist()
        
        sources = self.config.get('sources', {})
        successful_sources = 0
        
        for source_name, source_config in sources.items():
            if not source_config.get('enabled', True):
                continue
            
            print(f"📚 Processing {source_config['name']}...")
            
            if self._process_source(source_name, source_config):
                successful_sources += 1
                self.catalog['metadata']['sources'].append({
                    'name': source_name,
                    'display_name': source_config['name'],
                    'priority': source_config.get('priority', 1),
                    'processed_time': datetime.now().isoformat()
                })
            else:
                print(f"⚠️  Warning: Could not process {source_config['name']}")
        
        # Update metadata
        self.catalog['metadata']['total_wordlists'] = len(self.catalog['wordlists'])
        self.catalog['metadata']['processed_sources'] = successful_sources
        
        # Save unified catalog
        if successful_sources > 0 or len(self.catalog['wordlists']) > 0:
            return self._save_catalog()
        else:
            print("❌ No sources processed successfully")
            return False
    
    def _process_source(self, source_name: str, source_config: Dict) -> bool:
        """Process a specific wordlist source"""
        default_paths = source_config.get('default_paths', [])
        
        # Find the installed path
        source_path = None
        for path in default_paths:
            if os.path.exists(path):
                source_path = path
                break
        
        if not source_path:
            print(f"⚠️  {source_config['name']} not found in any default path")
            return False
        
        print(f"📂 Found {source_config['name']} at {source_path}")
        
        # Process based on source type
        if source_name == 'seclists':
            return self._process_seclists(source_path, source_config)
        elif source_name == 'jhaddix_all':
            return self._process_jhaddix_all(source_path, source_config)
        elif source_name == 'onelistforall':
            return self._process_onelistforall(source_path, source_config)
        else:
            print(f"⚠️  Unknown source type: {source_name}")
            return False
    
    def _process_seclists(self, base_path: str, config: Dict) -> bool:
        """Process SecLists wordlists"""
        try:
            wordlist_count = 0
            
            for root, dirs, files in os.walk(base_path):
                for file in files:
                    if file.endswith('.txt') and not file.startswith('.'):
                        full_path = os.path.join(root, file)
                        relative_path = os.path.relpath(full_path, base_path)
                        
                        # Get wordlist info
                        wordlist_info = self._analyze_wordlist(full_path, 'seclists', config)
                        if wordlist_info:
                            self.catalog['wordlists'][relative_path] = wordlist_info
                            wordlist_count += 1
            
            print(f"✅ Processed {wordlist_count} SecLists wordlists")
            return wordlist_count > 0
            
        except Exception as e:
            print(f"❌ Error processing SecLists: {e}")
            return False
    
    def _process_jhaddix_all(self, base_path: str, config: Dict) -> bool:
        """Process Jhaddix All.txt wordlist"""
        try:
            jhaddix_file = os.path.join(base_path, 'jhaddix-all.txt')
            
            if not os.path.exists(jhaddix_file):
                print(f"❌ Jhaddix All.txt not found at {jhaddix_file}")
                return False
            
            # Analyze the massive wordlist
            wordlist_info = self._analyze_wordlist(jhaddix_file, 'jhaddix_all', config)
            if wordlist_info:
                # Store with a consistent key
                relative_path = 'discovery/web-content/jhaddix-all.txt'
                
                # Enhanced metadata for Jhaddix
                wordlist_info.update({
                    'priority': 'critical',
                    'tags': ['comprehensive', 'discovery', 'jhaddix', 'king', 'premium'],
                    'description': 'The king of web content discovery - massive comprehensive wordlist',
                    'recommended_for': ['web_directories', 'web_files', 'comprehensive_discovery'],
                    'author': 'Jason Haddix',
                    'reputation': 'legendary'
                })
                
                self.catalog['wordlists'][relative_path] = wordlist_info
                print(f"✅ Processed Jhaddix All.txt - the crown jewel of web discovery")
                return True
            
            return False
            
        except Exception as e:
            print(f"❌ Error processing Jhaddix All.txt: {e}")
            return False
    
    def _process_onelistforall(self, base_path: str, config: Dict) -> bool:
        """Process OneListForAll wordlist collection"""
        try:
            wordlist_count = 0
            
            # Process consolidated file first (highest priority)
            consolidated_file = os.path.join(base_path, 'onelistforall-consolidated.txt')
            if os.path.exists(consolidated_file):
                wordlist_info = self._analyze_wordlist(consolidated_file, 'onelistforall', config)
                if wordlist_info:
                    relative_path = 'discovery/web-content/onelistforall-consolidated.txt'
                    
                    # Enhanced metadata for consolidated OneListForAll
                    wordlist_info.update({
                        'priority': 'high',
                        'tags': ['comprehensive', 'fuzzing', 'onelistforall', 'consolidated'],
                        'description': 'Comprehensive web fuzzing wordlist - consolidated from OneListForAll',
                        'recommended_for': ['web_directories', 'web_files', 'web_parameters'],
                        'author': 'six2dez',
                        'type': 'consolidated'
                    })
                    
                    self.catalog['wordlists'][relative_path] = wordlist_info
                    wordlist_count += 1
            
            # Process individual wordlists
            for root, dirs, files in os.walk(base_path):
                for file in files:
                    if (file.endswith('.txt') and 
                        not file.startswith('.') and 
                        'readme' not in file.lower() and
                        file != 'onelistforall-consolidated.txt'):
                        
                        full_path = os.path.join(root, file)
                        relative_path = os.path.relpath(full_path, base_path)
                        
                        # Get wordlist info
                        wordlist_info = self._analyze_wordlist(full_path, 'onelistforall', config)
                        if wordlist_info:
                            # Prefix with source for organization
                            catalog_key = f"onelistforall/{relative_path}"
                            self.catalog['wordlists'][catalog_key] = wordlist_info
                            wordlist_count += 1
            
            print(f"✅ Processed {wordlist_count} OneListForAll wordlists")
            return wordlist_count > 0
            
        except Exception as e:
            print(f"❌ Error processing OneListForAll: {e}")
            return False
    
    def _analyze_wordlist(self, file_path: str, source: str, config: Dict) -> Optional[Dict]:
        """Analyze a wordlist file and extract metadata"""
        try:
            stat = os.stat(file_path)
            size_kb = stat.st_size // 1024
            
            # Count lines efficiently (with limit for huge files)
            line_count = 0
            sample_content = []
            
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                for i, line in enumerate(f):
                    if i > 2000000:  # Cap at 2M lines for performance
                        line_count = 2000000
                        break
                    
                    # Collect sample for analysis
                    if i < 10:
                        sample_content.append(line.strip())
                    
                    line_count = i + 1
            
            # Categorize wordlist based on path and content
            category = self._categorize_wordlist(file_path, sample_content)
            
            # Extract tags from path and filename
            tags = self._extract_tags(file_path.lower(), source)
            
            # Determine priority based on source and characteristics
            priority = self._determine_priority(source, config, line_count, category)
            
            return {
                'source': source,
                'category': category,
                'lines': line_count,
                'size_kb': size_kb,
                'priority': priority,
                'tags': tags,
                'file_hash': self._calculate_file_hash(file_path),
                'last_modified': datetime.fromtimestamp(stat.st_mtime).isoformat(),
                'analyzed_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            print(f"⚠️  Warning: Could not analyze {file_path}: {e}")
            return None
    
    def _categorize_wordlist(self, file_path: str, sample_content: List[str]) -> str:
        """Categorize wordlist based on path and content analysis"""
        path_lower = file_path.lower()
        filename = os.path.basename(path_lower)
        
        # Category detection based on path
        if any(term in path_lower for term in ['web-content', 'discovery', 'directory', 'dir']):
            return 'web'
        elif any(term in path_lower for term in ['usernames', 'user']):
            return 'usernames'
        elif any(term in path_lower for term in ['passwords', 'pass']):
            return 'passwords'
        elif any(term in path_lower for term in ['dns', 'subdomain', 'vhost']):
            return 'dns'
        elif any(term in path_lower for term in ['snmp']):
            return 'snmp'
        elif any(term in path_lower for term in ['fuzzing', 'fuzz']):
            return 'fuzzing'
        elif any(term in path_lower for term in ['parameters', 'param']):
            return 'parameters'
        else:
            # Analyze content for better categorization
            if sample_content:
                # Look for common web patterns
                web_patterns = ['/', '.php', '.html', '.asp', 'admin', 'login']
                if any(any(pattern in line for pattern in web_patterns) for line in sample_content):
                    return 'web'
            
            return 'other'
    
    def _extract_tags(self, file_path: str, source: str) -> List[str]:
        """Extract relevant tags from file path and source"""
        tags = [source]
        
        # Technology-specific tags
        if 'wordpress' in file_path:
            tags.extend(['wordpress', 'cms'])
        if 'drupal' in file_path:
            tags.extend(['drupal', 'cms'])
        if 'joomla' in file_path:
            tags.extend(['joomla', 'cms'])
        
        # Size-based tags
        if any(term in file_path for term in ['big', 'large', 'huge']):
            tags.append('large')
        elif any(term in file_path for term in ['small', 'mini']):
            tags.append('small')
        elif any(term in file_path for term in ['medium', 'common']):
            tags.append('medium')
        
        # Content-type tags
        if any(term in file_path for term in ['comprehensive', 'all', 'complete']):
            tags.append('comprehensive')
        if any(term in file_path for term in ['quick', 'fast']):
            tags.append('quick')
        
        return list(set(tags))  # Remove duplicates
    
    def _determine_priority(self, source: str, config: Dict, line_count: int, category: str) -> str:
        """Determine wordlist priority based on multiple factors"""
        source_priority = config.get('priority', 1)
        
        # Base priority from source
        if source_priority >= 5:
            base_priority = 'critical'
        elif source_priority >= 4:
            base_priority = 'high'
        elif source_priority >= 3:
            base_priority = 'medium'
        else:
            base_priority = 'low'
        
        # Adjust based on comprehensiveness and category
        if category == 'web' and line_count > 100000:
            if base_priority in ['high', 'medium']:
                return 'critical'
            elif base_priority == 'low':
                return 'medium'
        
        return base_priority
    
    def _check_for_mega_wordlist(self) -> bool:
        """Check for and prioritize the consolidated mega-wordlist"""
        try:
            installation_base = self.config.get('installation', {}).get('base_directory', '/usr/share/wordlists')
            mega_wordlist_paths = [
                f"{installation_base}/ipcrawler/ipcrawler-mega-wordlist.txt",
                f"{installation_base}/ipcrawler-mega.txt",
                "/usr/local/share/wordlists/ipcrawler/ipcrawler-mega-wordlist.txt",
                "/opt/wordlists/ipcrawler/ipcrawler-mega-wordlist.txt"
            ]
            
            for mega_path in mega_wordlist_paths:
                if os.path.exists(mega_path):
                    print(f"🏆 Found consolidated mega-wordlist: {mega_path}")
                    
                    # Analyze the mega-wordlist
                    wordlist_info = self._analyze_mega_wordlist(mega_path)
                    if wordlist_info:
                        # Store with ultimate priority key
                        catalog_key = 'discovery/web-content/ipcrawler-mega-wordlist.txt'
                        
                        # Enhanced metadata for mega-wordlist
                        wordlist_info.update({
                            'priority': 'ultimate',
                            'tags': ['consolidated', 'mega', 'comprehensive', 'premium', 'ipcrawler'],
                            'description': 'ipcrawler consolidated mega-wordlist from all premium sources',
                            'recommended_for': ['web_directories', 'web_files', 'comprehensive_discovery'],
                            'sources': ['jhaddix_all', 'onelistforall', 'seclists'],
                            'type': 'mega-wordlist',
                            'reputation': 'ultimate'
                        })
                        
                        self.catalog['wordlists'][catalog_key] = wordlist_info
                        print(f"✅ Mega-wordlist added to catalog with ultimate priority")
                        return True
            
            print("📝 No consolidated mega-wordlist found - will be created after installation")
            return False
            
        except Exception as e:
            print(f"⚠️  Warning: Could not check for mega-wordlist: {e}")
            return False
    
    def _analyze_mega_wordlist(self, file_path: str) -> Optional[Dict]:
        """Analyze the mega-wordlist with special handling"""
        try:
            stat = os.stat(file_path)
            size_kb = stat.st_size // 1024
            
            # Count lines efficiently for mega-wordlist
            line_count = 0
            sample_content = []
            
            print(f"📊 Analyzing mega-wordlist: {os.path.basename(file_path)}")
            
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                for i, line in enumerate(f):
                    if i > 5000000:  # Cap at 5M lines for performance
                        line_count = 5000000
                        break
                    
                    # Collect sample for analysis
                    if i < 20:
                        sample_content.append(line.strip())
                    
                    line_count = i + 1
                    
                    # Progress indicator for large files
                    if i > 0 and i % 500000 == 0:
                        print(f"   📈 Analyzed {i:,} lines...")
            
            print(f"📋 Mega-wordlist contains {line_count:,} unique entries")
            
            return {
                'source': 'ipcrawler_mega',
                'category': 'web',
                'lines': line_count,
                'size_kb': size_kb,
                'priority': 'ultimate',
                'tags': ['mega', 'consolidated'],
                'file_hash': self._calculate_file_hash(file_path),
                'last_modified': datetime.fromtimestamp(stat.st_mtime).isoformat(),
                'analyzed_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            print(f"⚠️  Warning: Could not analyze mega-wordlist {file_path}: {e}")
            return None
    
    def _calculate_file_hash(self, file_path: str) -> str:
        """Calculate SHA256 hash of file for integrity verification"""
        try:
            hasher = hashlib.sha256()
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hasher.update(chunk)
            return hasher.hexdigest()[:16]  # First 16 chars for space efficiency
        except:
            return 'unknown'
    
    def _save_catalog(self) -> bool:
        """Save the unified catalog to file"""
        try:
            # Determine catalog file path
            script_dir = Path(__file__).parent.parent
            catalog_path = script_dir / "data" / "unified_catalog.yaml"
            
            # Ensure directory exists
            catalog_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Save catalog
            with open(catalog_path, 'w') as f:
                yaml.dump(self.catalog, f, default_flow_style=False, sort_keys=False)
            
            print(f"✅ Unified catalog saved to {catalog_path}")
            print(f"📊 Catalog contains {len(self.catalog['wordlists'])} wordlists from {len(self.catalog['metadata']['sources'])} sources")
            
            # Print source summary
            for source_info in self.catalog['metadata']['sources']:
                source_wordlists = [w for w in self.catalog['wordlists'].values() 
                                  if w['source'] == source_info['name']]
                print(f"   📚 {source_info['display_name']}: {len(source_wordlists)} wordlists")
            
            return True
            
        except Exception as e:
            print(f"❌ Failed to save catalog: {e}")
            return False