"""
Enhanced Wordlist Manager for ipcrawler
Manages multiple wordlist sources and creates unified catalogs
"""

import os
import yaml
import requests
import subprocess
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple
from datetime import datetime, timedelta
import hashlib
import shutil
from urllib.parse import urlparse


class EnhancedWordlistManager:
    """Enhanced wordlist manager supporting multiple sources"""
    
    def __init__(self, config_path: str = None):
        """Initialize the enhanced wordlist manager"""
        self.config_path = config_path or self._get_default_config_path()
        self.config = self._load_config()
        self.catalog_cache = {}
        
    def _get_default_config_path(self) -> str:
        """Get default configuration path"""
        script_dir = Path(__file__).parent.parent
        return str(script_dir / "data" / "wordlist_sources.yaml")
    
    def _load_config(self) -> Dict:
        """Load wordlist sources configuration"""
        try:
            with open(self.config_path, 'r') as f:
                return yaml.safe_load(f)
        except Exception as e:
            print(f"❌ Failed to load wordlist config: {e}")
            return self._get_fallback_config()
    
    def _get_fallback_config(self) -> Dict:
        """Fallback configuration if file load fails"""
        return {
            'sources': {
                'seclists': {
                    'name': 'SecLists',
                    'default_paths': ['/usr/share/seclists'],
                    'priority': 3,
                    'enabled': True
                }
            },
            'installation': {
                'base_directory': '/usr/share/wordlists'
            }
        }
    
    def install_all_wordlists(self, force_reinstall: bool = False) -> Dict[str, bool]:
        """Install all configured wordlist sources and create consolidated wordlist"""
        results = {}
        sources = self.config.get('sources', {})
        
        print("🚀 Installing comprehensive wordlist collection for ipcrawler...")
        print(f"📦 Found {len(sources)} wordlist sources to install")
        
        for source_name, source_config in sources.items():
            if not source_config.get('enabled', True):
                print(f"⏭️  Skipping disabled source: {source_name}")
                results[source_name] = False
                continue
            
            print(f"\n📥 Installing {source_config['name']}...")
            try:
                success = self._install_source(source_name, source_config, force_reinstall)
                results[source_name] = success
                if success:
                    print(f"✅ {source_config['name']} installed successfully")
                else:
                    print(f"❌ Failed to install {source_config['name']}")
            except Exception as e:
                print(f"❌ Error installing {source_name}: {e}")
                results[source_name] = False
        
        # Create consolidated mega-wordlist after installation
        if any(results.values()):
            print(f"\n🔄 Creating consolidated mega-wordlist...")
            consolidated_success = self._create_consolidated_wordlist()
            if consolidated_success:
                print(f"✅ Consolidated mega-wordlist created successfully")
            
            print(f"\n🔄 Rebuilding unified wordlist catalog...")
            self.rebuild_unified_catalog()
            print(f"✅ Unified catalog rebuilt successfully")
        
        return results
    
    def _install_source(self, source_name: str, config: Dict, force: bool = False) -> bool:
        """Install a specific wordlist source"""
        install_methods = config.get('install_methods', {})
        default_paths = config.get('default_paths', [])
        
        # Check if already installed (unless forcing reinstall)
        if not force:
            for path in default_paths:
                if os.path.exists(path):
                    print(f"✅ {config['name']} already found at {path}")
                    return True
        
        # Try different installation methods
        if source_name == 'seclists':
            return self._install_seclists(install_methods, default_paths)
        elif source_name == 'jhaddix_all':
            return self._install_jhaddix_all(config)
        elif source_name == 'onelistforall':
            return self._install_onelistforall(config)
        else:
            print(f"⚠️  Unknown installation method for {source_name}")
            return False
    
    def _install_seclists(self, methods: Dict, paths: List[str]) -> bool:
        """Install SecLists using various methods"""
        # Check if already installed
        for path in paths:
            if os.path.exists(path):
                return True
        
        # Try package manager first
        if self._try_package_install('seclists'):
            return True
        
        # Try manual git clone
        target_path = paths[0] if paths else '/usr/share/seclists'
        return self._try_git_install(
            'https://github.com/danielmiessler/SecLists.git',
            target_path
        )
    
    def _install_jhaddix_all(self, config: Dict) -> bool:
        """Install Jhaddix All.txt wordlist"""
        url = config.get('url')
        default_paths = config.get('default_paths', [])
        
        if not url:
            print("❌ No URL configured for Jhaddix All.txt")
            return False
        
        # Create target directory
        target_dir = default_paths[0] if default_paths else '/usr/share/wordlists/jhaddix'
        os.makedirs(target_dir, exist_ok=True)
        target_file = os.path.join(target_dir, 'jhaddix-all.txt')
        
        # Download the wordlist
        try:
            print(f"📥 Downloading Jhaddix All.txt from {url}")
            response = requests.get(url, stream=True, timeout=30)
            response.raise_for_status()
            
            with open(target_file, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            # Verify download
            if os.path.exists(target_file) and os.path.getsize(target_file) > 1000000:  # At least 1MB
                print(f"✅ Jhaddix All.txt downloaded to {target_file}")
                
                # Get file stats
                size_mb = os.path.getsize(target_file) / (1024 * 1024)
                with open(target_file, 'r', encoding='utf-8', errors='ignore') as f:
                    line_count = sum(1 for _ in f)
                
                print(f"📊 Jhaddix All.txt: {line_count:,} lines, {size_mb:.1f}MB")
                return True
            else:
                print("❌ Downloaded file appears to be corrupted or too small")
                return False
                
        except Exception as e:
            print(f"❌ Failed to download Jhaddix All.txt: {e}")
            return False
    
    def _install_onelistforall(self, config: Dict) -> bool:
        """Install OneListForAll wordlist collection"""
        repo_url = config.get('repository')
        default_paths = config.get('default_paths', [])
        
        if not repo_url:
            print("❌ No repository URL configured for OneListForAll")
            return False
        
        target_dir = default_paths[0] if default_paths else '/usr/share/wordlists/onelistforall'
        
        # Try git clone
        success = self._try_git_install(repo_url, target_dir)
        
        if success:
            # Consolidate OneListForAll into a single comprehensive file
            consolidated_file = os.path.join(target_dir, 'onelistforall-consolidated.txt')
            if self._consolidate_onelistforall(target_dir, consolidated_file):
                print(f"✅ OneListForAll consolidated into {consolidated_file}")
            
        return success
    
    def _consolidate_onelistforall(self, source_dir: str, output_file: str) -> bool:
        """Consolidate OneListForAll wordlists into a single file"""
        try:
            wordlists = []
            
            # Find all .txt files in the OneListForAll directory
            for root, dirs, files in os.walk(source_dir):
                for file in files:
                    if file.endswith('.txt') and 'readme' not in file.lower():
                        wordlists.append(os.path.join(root, file))
            
            if not wordlists:
                print("⚠️  No wordlist files found in OneListForAll")
                return False
            
            print(f"🔄 Consolidating {len(wordlists)} OneListForAll wordlists...")
            
            # Consolidate with deduplication
            all_words = set()
            for wordlist_file in wordlists:
                try:
                    with open(wordlist_file, 'r', encoding='utf-8', errors='ignore') as f:
                        for line in f:
                            word = line.strip()
                            if word and not word.startswith('#'):  # Skip empty lines and comments
                                all_words.add(word)
                except Exception as e:
                    print(f"⚠️  Warning: Could not read {wordlist_file}: {e}")
            
            # Write consolidated file
            with open(output_file, 'w', encoding='utf-8') as f:
                for word in sorted(all_words):
                    f.write(f"{word}\n")
            
            print(f"📊 OneListForAll consolidated: {len(all_words):,} unique entries")
            return True
            
        except Exception as e:
            print(f"❌ Failed to consolidate OneListForAll: {e}")
            return False
    
    def _try_package_install(self, package: str) -> bool:
        """Try installing package via system package manager"""
        try:
            # Try apt (Debian/Ubuntu)
            if shutil.which('apt'):
                result = subprocess.run(['sudo', 'apt', 'install', '-y', package], 
                                      capture_output=True, text=True, timeout=300)
                if result.returncode == 0:
                    return True
            
            # Try brew (macOS)
            if shutil.which('brew'):
                result = subprocess.run(['brew', 'install', package], 
                                      capture_output=True, text=True, timeout=300)
                if result.returncode == 0:
                    return True
            
            # Try yum (RedHat/CentOS)
            if shutil.which('yum'):
                result = subprocess.run(['sudo', 'yum', 'install', '-y', package], 
                                      capture_output=True, text=True, timeout=300)
                if result.returncode == 0:
                    return True
                    
        except Exception as e:
            print(f"⚠️  Package install failed: {e}")
        
        return False
    
    def _try_git_install(self, repo_url: str, target_path: str) -> bool:
        """Try installing via git clone"""
        try:
            if os.path.exists(target_path):
                print(f"🔄 Directory exists, updating: {target_path}")
                result = subprocess.run(['git', 'pull'], cwd=target_path, 
                                      capture_output=True, text=True, timeout=300)
            else:
                print(f"📥 Cloning {repo_url} to {target_path}")
                os.makedirs(os.path.dirname(target_path), exist_ok=True)
                result = subprocess.run(['git', 'clone', repo_url, target_path], 
                                      capture_output=True, text=True, timeout=600)
            
            return result.returncode == 0
            
        except Exception as e:
            print(f"❌ Git install failed: {e}")
            return False
    
    def rebuild_unified_catalog(self) -> bool:
        """Rebuild the unified wordlist catalog from all sources"""
        from .catalog_builder import UnifiedCatalogBuilder
        
        try:
            builder = UnifiedCatalogBuilder(self.config)
            return builder.build_catalog()
        except Exception as e:
            print(f"❌ Failed to rebuild catalog: {e}")
            return False
    
    def get_available_sources(self) -> Dict[str, Dict]:
        """Get all available wordlist sources with their status"""
        sources_status = {}
        sources = self.config.get('sources', {})
        
        for source_name, source_config in sources.items():
            status = {
                'name': source_config['name'],
                'description': source_config.get('description', ''),
                'enabled': source_config.get('enabled', True),
                'priority': source_config.get('priority', 1),
                'installed': False,
                'path': None
            }
            
            # Check if installed
            for path in source_config.get('default_paths', []):
                if os.path.exists(path):
                    status['installed'] = True
                    status['path'] = path
                    break
            
            sources_status[source_name] = status
        
        return sources_status
    
    def _create_consolidated_wordlist(self) -> bool:
        """Create a single consolidated mega-wordlist from all premium sources"""
        try:
            print("🔍 Gathering wordlists for consolidation...")
            
            # Find all installed wordlist sources
            all_wordlists = set()
            wordlist_files = []
            
            installation_base = self.config.get('installation', {}).get('base_directory', '/usr/share/wordlists')
            
            # 1. Jhaddix All.txt (highest priority)
            jhaddix_paths = [
                f"{installation_base}/jhaddix/jhaddix-all.txt",
                "/usr/local/share/wordlists/jhaddix/jhaddix-all.txt",
                "/opt/wordlists/jhaddix/jhaddix-all.txt"
            ]
            
            for jhaddix_path in jhaddix_paths:
                if os.path.exists(jhaddix_path):
                    wordlist_files.append(('jhaddix', jhaddix_path))
                    print(f"🏆 Found Jhaddix All.txt: {jhaddix_path}")
                    break
            
            # 2. OneListForAll consolidated
            onelistforall_paths = [
                f"{installation_base}/onelistforall/onelistforall-consolidated.txt",
                "/usr/local/share/wordlists/onelistforall/onelistforall-consolidated.txt",
                "/opt/wordlists/onelistforall/onelistforall-consolidated.txt"
            ]
            
            for onelist_path in onelistforall_paths:
                if os.path.exists(onelist_path):
                    wordlist_files.append(('onelistforall', onelist_path))
                    print(f"📚 Found OneListForAll: {onelist_path}")
                    break
            
            # 3. Key SecLists files
            seclists_paths = [
                "/usr/share/seclists",
                "/usr/share/SecLists", 
                "/opt/SecLists",
                "/usr/local/share/seclists"
            ]
            
            for seclists_base in seclists_paths:
                if os.path.exists(seclists_base):
                    # Add key SecLists files
                    key_seclists = [
                        f"{seclists_base}/Discovery/Web-Content/directory-list-2.3-medium.txt",
                        f"{seclists_base}/Discovery/Web-Content/common.txt",
                        f"{seclists_base}/Discovery/Web-Content/raft-medium-directories.txt",
                        f"{seclists_base}/Discovery/Web-Content/big.txt"
                    ]
                    
                    for seclists_file in key_seclists:
                        if os.path.exists(seclists_file):
                            wordlist_files.append(('seclists', seclists_file))
                            print(f"📋 Found SecLists: {os.path.basename(seclists_file)}")
                    break
            
            if not wordlist_files:
                print("❌ No wordlist sources found for consolidation")
                return False
            
            print(f"🔄 Consolidating {len(wordlist_files)} wordlist files...")
            
            # Read and consolidate all wordlists with deduplication
            for source, wordlist_path in wordlist_files:
                try:
                    print(f"   📖 Reading {source}: {os.path.basename(wordlist_path)}")
                    
                    with open(wordlist_path, 'r', encoding='utf-8', errors='ignore') as f:
                        for line_num, line in enumerate(f, 1):
                            word = line.strip()
                            if word and not word.startswith('#') and len(word) < 100:  # Skip comments and overly long lines
                                all_wordlists.add(word)
                            
                            # Progress indicator for large files
                            if line_num % 100000 == 0:
                                print(f"     📊 Processed {line_num:,} lines, unique words: {len(all_wordlists):,}")
                
                except Exception as e:
                    print(f"⚠️  Warning: Could not read {wordlist_path}: {e}")
                    continue
            
            # Create consolidated output directory
            output_dir = f"{installation_base}/ipcrawler"
            os.makedirs(output_dir, exist_ok=True)
            
            # Write consolidated mega-wordlist
            consolidated_file = f"{output_dir}/ipcrawler-mega-wordlist.txt"
            
            print(f"💾 Writing consolidated mega-wordlist to: {consolidated_file}")
            
            with open(consolidated_file, 'w', encoding='utf-8') as f:
                # Sort wordlists for consistency and better performance
                sorted_words = sorted(all_wordlists)
                for word in sorted_words:
                    f.write(f"{word}\n")
            
            # Get final statistics
            file_size = os.path.getsize(consolidated_file)
            size_mb = file_size / (1024 * 1024)
            
            print(f"✅ Consolidated mega-wordlist created!")
            print(f"📊 Final stats:")
            print(f"   📝 Unique entries: {len(all_wordlists):,}")
            print(f"   💾 File size: {size_mb:.1f} MB")
            print(f"   📂 Location: {consolidated_file}")
            
            # Create a symlink for easy access
            easy_access_link = f"{installation_base}/ipcrawler-mega.txt"
            try:
                if os.path.exists(easy_access_link):
                    os.remove(easy_access_link)
                os.symlink(consolidated_file, easy_access_link)
                print(f"🔗 Created easy access link: {easy_access_link}")
            except:
                pass  # Symlink creation might fail on some systems
            
            return True
            
        except Exception as e:
            print(f"❌ Error creating consolidated wordlist: {e}")
            return False

    def get_installation_summary(self) -> str:
        """Get a summary of wordlist installation status"""
        sources = self.get_available_sources()
        
        summary = "📊 ipcrawler Wordlist Installation Summary\n"
        summary += "=" * 50 + "\n\n"
        
        installed_count = 0
        total_count = len(sources)
        
        for source_name, status in sources.items():
            icon = "✅" if status['installed'] else "❌"
            priority = "⭐" * status['priority']
            summary += f"{icon} {status['name']} {priority}\n"
            if status['installed']:
                summary += f"   📂 {status['path']}\n"
                installed_count += 1
            else:
                summary += f"   ⚠️  Not installed\n"
            summary += f"   📝 {status['description']}\n\n"
        
        # Check for consolidated mega-wordlist
        installation_base = self.config.get('installation', {}).get('base_directory', '/usr/share/wordlists')
        mega_wordlist = f"{installation_base}/ipcrawler/ipcrawler-mega-wordlist.txt"
        
        if os.path.exists(mega_wordlist):
            try:
                # Get stats of consolidated wordlist
                file_size = os.path.getsize(mega_wordlist)
                size_mb = file_size / (1024 * 1024)
                
                with open(mega_wordlist, 'r') as f:
                    line_count = sum(1 for _ in f)
                
                summary += f"🏆 ipcrawler Mega-Wordlist ⭐⭐⭐⭐⭐\n"
                summary += f"   ✅ {mega_wordlist}\n"
                summary += f"   📊 {line_count:,} unique entries, {size_mb:.1f} MB\n"
                summary += f"   📝 Consolidated from all premium sources\n\n"
            except:
                summary += f"🏆 ipcrawler Mega-Wordlist ⭐⭐⭐⭐⭐\n"
                summary += f"   ✅ Available\n"
                summary += f"   📝 Consolidated from all premium sources\n\n"
        
        summary += f"📈 Status: {installed_count}/{total_count} sources installed\n"
        
        if installed_count < total_count:
            summary += "\n💡 To install missing wordlists:\n"
            summary += "   make install-wordlists\n"
            summary += "   # or\n"
            summary += "   ipcrawler --install-wordlists\n"
        
        return summary